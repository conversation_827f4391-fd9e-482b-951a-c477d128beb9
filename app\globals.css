@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 240 33% 5%;
  --foreground: 0 0% 100%;
  --primary: 270 86% 53%;
  --secondary: 171 60% 43%;
  --accent: 224 65% 76%;
  --muted: 0 0% 16%;
  --muted-foreground: 220 14% 91%;
  --border: 0 0% 16%;
}

body {
  background-color: #050816;
  color: #ffffff;
}

.gradient-progress {
  background: linear-gradient(261.81deg, #8c01fa -8.01%, #19fb9b 100%);
  border-radius: 80px;
}

.normal-progress {
  background: linear-gradient(0deg, #B9BCBE, #B9BCBE);
  border-radius: 80px;
}

.gradient-text {
  background: linear-gradient(263.61deg, #19fb9b 25.9%, #8c01fa 79.13%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.gradient-button {
  background: linear-gradient(90deg, #7f20ef 0%, #2cad9c 100%);
}

.prediction-button {
  background: linear-gradient(261.81deg, #19fb9b -8.01%, #8c01fa 100%);
}

.border-gradient {
  border-image: linear-gradient(90deg, #7f20ef, #2cad9c) 1;
}

@keyframes slideIn {
  0% {
    opacity: 0;
    transform: translateY(-8px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes menuBounce {
  0% {
    transform: scale(0.95) translateY(-4px);
  }

  50% {
    transform: scale(1.02) translateY(0);
  }

  100% {
    transform: scale(1) translateY(0);
  }
}

.menu-open {
  animation: menuBounce 0.4s ease-out forwards;
}

/* Animated Background Blurs */
@keyframes floatBlur1 {
  0% {
    transform: rotate(-164.56deg) translate(0, 0);
  }
  25% {
    transform: rotate(-164.56deg) translate(30px, -20px);
  }
  50% {
    transform: rotate(-164.56deg) translate(-20px, 30px);
  }
  75% {
    transform: rotate(-164.56deg) translate(40px, 10px);
  }
  100% {
    transform: rotate(-164.56deg) translate(0, 0);
  }
}

@keyframes floatBlur2 {
  0% {
    transform: rotate(-164.56deg) translate(0, 0);
  }
  25% {
    transform: rotate(-164.56deg) translate(-40px, 20px);
  }
  50% {
    transform: rotate(-164.56deg) translate(25px, -30px);
  }
  75% {
    transform: rotate(-164.56deg) translate(-15px, -10px);
  }
  100% {
    transform: rotate(-164.56deg) translate(0, 0);
  }
}

/* Gradient Text Hover Effects */
.gradient-text {
  background: linear-gradient(263.61deg, #19fb9b 25.9%, #8c01fa 79.13%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  transition: all 0.3s ease;
  position: relative;
}

.gradient-text:hover {
  background: linear-gradient(263.61deg, #19fb9b 0%, #8c01fa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transform: scale(1.02);
  filter: brightness(1.2);
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-text-animated {
  background: linear-gradient(45deg, #19fb9b, #8c01fa, #19fb9b, #8c01fa);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease infinite;
}

/* Binary Code Logo Effect */
@keyframes binaryFlowLeftToRight {
  0% {
    transform: translateX(-150%);
  }
  100% {
    transform: translateX(150%);
  }
}

@keyframes binaryFlowRightToLeft {
  0% {
    transform: translateX(150%);
  }
  100% {
    transform: translateX(-150%);
  }
}

.logo-binary-container {
  position: relative;
  display: inline-block;
}

.logo-binary-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  mask: url('/white_horse_icon.svg') no-repeat center;
  mask-size: contain;
  -webkit-mask: url('/white_horse_icon.svg') no-repeat center;
  -webkit-mask-size: contain;
  z-index: 2;
}

.binary-stream {
  position: absolute;
  font-family: 'Courier New', monospace;
  font-size: 8px;
  line-height: 10px;
  color: #19fb9b;
  white-space: nowrap;
  text-shadow: 0 0 3px #19fb9b;
  width: 400%;
  left: -200%;
  height: 6px;
  text-align: center;
  letter-spacing: 1px;
}

.binary-stream:nth-child(1) { top: 0%; animation: binaryFlowLeftToRight 18s linear infinite; animation-delay: -3.7s; }
.binary-stream:nth-child(2) { top: 3%; animation: binaryFlowRightToLeft 22s linear infinite; animation-delay: -12.1s; }
.binary-stream:nth-child(3) { top: 6%; animation: binaryFlowLeftToRight 16s linear infinite; animation-delay: -7.3s; }
.binary-stream:nth-child(4) { top: 9%; animation: binaryFlowRightToLeft 25s linear infinite; animation-delay: -15.8s; }
.binary-stream:nth-child(5) { top: 12%; animation: binaryFlowLeftToRight 19s linear infinite; animation-delay: -2.4s; }
.binary-stream:nth-child(6) { top: 15%; animation: binaryFlowRightToLeft 21s linear infinite; animation-delay: -9.6s; }
.binary-stream:nth-child(7) { top: 18%; animation: binaryFlowLeftToRight 17s linear infinite; animation-delay: -18.2s; }
.binary-stream:nth-child(8) { top: 21%; animation: binaryFlowRightToLeft 23s linear infinite; animation-delay: -5.9s; }
.binary-stream:nth-child(9) { top: 24%; animation: binaryFlowLeftToRight 20s linear infinite; animation-delay: -14.3s; }
.binary-stream:nth-child(10) { top: 27%; animation: binaryFlowRightToLeft 24s linear infinite; animation-delay: -8.7s; }
.binary-stream:nth-child(11) { top: 30%; animation: binaryFlowLeftToRight 18s linear infinite; animation-delay: -11.4s; }
.binary-stream:nth-child(12) { top: 33%; animation: binaryFlowRightToLeft 26s linear infinite; animation-delay: -4.2s; }
.binary-stream:nth-child(13) { top: 36%; animation: binaryFlowLeftToRight 15s linear infinite; animation-delay: -16.8s; }
.binary-stream:nth-child(14) { top: 39%; animation: binaryFlowRightToLeft 22s linear infinite; animation-delay: -1.9s; }
.binary-stream:nth-child(15) { top: 42%; animation: binaryFlowLeftToRight 19s linear infinite; animation-delay: -13.5s; }
.binary-stream:nth-child(16) { top: 45%; animation: binaryFlowRightToLeft 27s linear infinite; animation-delay: -6.7s; }
.binary-stream:nth-child(17) { top: 48%; animation: binaryFlowLeftToRight 17s linear infinite; animation-delay: -19.1s; }
.binary-stream:nth-child(18) { top: 51%; animation: binaryFlowRightToLeft 21s linear infinite; animation-delay: -0.8s; }
.binary-stream:nth-child(19) { top: 54%; animation: binaryFlowLeftToRight 16s linear infinite; animation-delay: -17.3s; }
.binary-stream:nth-child(20) { top: 57%; animation: binaryFlowRightToLeft 24s linear infinite; animation-delay: -10.2s; }
.binary-stream:nth-child(21) { top: 60%; animation: binaryFlowLeftToRight 18s linear infinite; animation-delay: -14.7s; }
.binary-stream:nth-child(22) { top: 63%; animation: binaryFlowRightToLeft 23s linear infinite; animation-delay: -3.1s; }
.binary-stream:nth-child(23) { top: 66%; animation: binaryFlowLeftToRight 20s linear infinite; animation-delay: -11.8s; }
.binary-stream:nth-child(24) { top: 69%; animation: binaryFlowRightToLeft 25s linear infinite; animation-delay: -7.4s; }
.binary-stream:nth-child(25) { top: 72%; animation: binaryFlowLeftToRight 17s linear infinite; animation-delay: -19.6s; }
.binary-stream:nth-child(26) { top: 75%; animation: binaryFlowRightToLeft 19s linear infinite; animation-delay: -5.3s; }
.binary-stream:nth-child(27) { top: 78%; animation: binaryFlowLeftToRight 21s linear infinite; animation-delay: -13.9s; }
.binary-stream:nth-child(28) { top: 81%; animation: binaryFlowRightToLeft 16s linear infinite; animation-delay: -8.1s; }
.binary-stream:nth-child(29) { top: 84%; animation: binaryFlowLeftToRight 24s linear infinite; animation-delay: -16.4s; }
.binary-stream:nth-child(30) { top: 87%; animation: binaryFlowRightToLeft 18s linear infinite; animation-delay: -2.7s; }
.binary-stream:nth-child(31) { top: 90%; animation: binaryFlowLeftToRight 22s linear infinite; animation-delay: -11.2s; }
.binary-stream:nth-child(32) { top: 93%; animation: binaryFlowRightToLeft 20s linear infinite; animation-delay: -6.8s; }
.binary-stream:nth-child(33) { top: 96%; animation: binaryFlowLeftToRight 26s linear infinite; animation-delay: -15.1s; }
.binary-stream:nth-child(34) { top: 99%; animation: binaryFlowRightToLeft 17s linear infinite; animation-delay: -9.5s; }
.binary-stream:nth-child(35) { top: 102%; animation: binaryFlowLeftToRight 23s linear infinite; animation-delay: -18.7s; }

.logo-base {
  opacity: 0.3;
  position: relative;
  z-index: 1;
  filter: hue-rotate(240deg) saturate(2) brightness(0.8);
}

/* Enhanced Button Hover Effects */
@keyframes buttonPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(25, 251, 155, 0.4);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(25, 251, 155, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(25, 251, 155, 0);
  }
}

@keyframes gradientShimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

.gradient-button-enhanced {
  background: linear-gradient(90deg, #8C01FA 0%, #19FB9B 100%);
  background-size: 200% 100%;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.gradient-button-enhanced:hover {
  transform: translateY(-2px);
  animation: buttonPulse 1.5s infinite, gradientShimmer 2s ease-in-out infinite;
  filter: brightness(1.1);
}

.gradient-button-enhanced:active {
  transform: translateY(0);
}

.gradient-button-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.gradient-button-enhanced:hover::before {
  left: 100%;
}

/* Prediction Button Specific Styling */
.prediction-button {
  background: linear-gradient(261.81deg, #19fb9b -8.01%, #8c01fa 100%);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.prediction-button:hover {
  transform: translateY(-2px);
  filter: brightness(1.15);
  box-shadow: 0 8px 25px rgba(25, 251, 155, 0.3);
}

.prediction-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
  border-radius: 50%;
}

.prediction-button:hover::after {
  width: 300px;
  height: 300px;
}

.tab-button {
  position: relative;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 700;
  color: #9CA2B5;
  background: transparent;
  width: 100%;
}

.tab-button.active {
  color: white;
}

.tab-button.active::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 8px;
  background: linear-gradient(0deg, rgba(11, 10, 10, 0.4), rgba(0, 0, 0, 0.4)),
    linear-gradient(261.81deg, rgba(25, 251, 155, 0.4) -8.01%, rgba(140, 1, 250, 0.4) 100%);
}

.tab-button.active::after {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 8px;
  padding: 1.5px;
  background: linear-gradient(261.81deg, #19FB9B -8.01%, #8C01FA 100%);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

@media (min-width: 768px) {
  .tab-button-container {
    display: none;
  }

  .tab-content {
    display: block;
  }
}

@media (max-height: 300px) {
  .h-full {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }

  button,
  .icon {
    transform: scale(0.8);
  }

  .gap-4 {
    gap: 0.5rem !important;
  }

  .ScrollArea {
    max-height: 150px !important;
  }

  header,
  .header {
    min-height: 40px !important;
  }
}

@media (max-width: 400px) {

  .px-3,
  .px-4 {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }

  .gap-4,
  .gap-3 {
    gap: 0.5rem !important;
  }

  .min-w-320px {
    min-width: calc(100% - 0.5rem) !important;
  }

  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: repeat(auto-fit, minmax(70px, 1fr)) !important;
  }

  .text-sm {
    font-size: 0.75rem !important;
  }

  button {
    padding: 0.25rem 0.5rem !important;
  }
}